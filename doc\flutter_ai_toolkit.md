Hello and welcome to the Flutter AI Toolkit!

The AI Toolkit is a set of AI chat-related widgets to make it easy to add an AI chat window to your Flutter app. The AI Toolkit is organized around an abstract LLM provider API that makes it easy to swap out the LLM provider that you'd like your chat provider to use. Out of the box, it comes with support for one LLM provider integration: Firebase AI.

Key features 
Multi-turn chat: Maintains context across multiple interactions.
Streaming responses: Displays AI responses in real-time as they're generated.
Rich text display: Supports formatted text in chat messages.
Voice input: Allows users to input prompts using speech.
Multimedia attachments: Enables sending and receiving various media types.
Custom styling: Offers extensive customization to match your app's design.
Chat serialization/deserialization: Store and retrieve conversations between app sessions.
Pluggable LLM support: Implement a simple interface to plug in your own LLM.
Cross-platform support: Compatible with the Android, iOS, web, and macOS platforms.
Migration to v0.9.0 
The v0.9.0 release marks the first real breaking change since the initial release of the Flutter AI Toolkit. This change was brought on by the migration from the google_generative_ai and firebase_vertexai packages to the new firebase_ai package.

One change is that there is now a single FirebaseProvider that works for both Gemini and Vertex. Both of these providers served up the same models in the past, e.g. gemini-2.0-flash, but did so via different mechanisms; one was via an API key and the other was via a Firebase project. The other real difference was the billing model. When using the firebase_ai package, API key support has been dropped in favor of always requiring a Firebase project. The only remaining difference is the billing model, which you can read about in the Firebase docs.

To migrate, the following code using the GeminiProvider:

    class ChatPage extends StatelessWidget {
      const ChatPage({super.key, required this.title});
      final String title;

      @override
      Widget build(BuildContext context) {
        return Scaffold(
          appBar: AppBar(title: Text(title)),
          body: LlmChatView(
            provider: GeminiProvider( // this changes
              model: GenerativeModel( // and this changes
                model: 'gemini-2.0-flash',
                apiKey: 'GEMINI-API-KEY', // and this changes
              ),
            ),
          ),
        );
      }
    }
now becomes code that uses googleAI():

class ChatPage extends StatelessWidget {
  const ChatPage({super.key});

  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text(App.title)),
    body: LlmChatView(
      provider: FirebaseProvider( // use FirebaseProvider and googleAI()
        model: FirebaseAI.googleAI().generativeModel(model: 'gemini-2.0-flash'),
      ),
    ),
  );
}
And the following code using the VertexProvider:

class ChatPage extends StatelessWidget {
  const ChatPage({super.key});

  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: AppBar(title: const Text(App.title)),
        body: LlmChatView(
          provider: VertexProvider( // this changes
            chatModel: FirebaseVertexAI.instance.generativeModel( // and this
              model: 'gemini-2.0-flash',
            ),
          ),
        ),
      );
}
becomes code that uses vertexAI():

class ChatPage extends StatelessWidget {
  const ChatPage({super.key});

  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text(App.title)),
    body: LlmChatView(
      provider: FirebaseProvider( // use FirebaseProvider and vertexAI()
        model: FirebaseAI.vertexAI().generativeModel(model: 'gemini-2.0-flash'),
      ),
    ),
  );
}
Also, all projects must now be initialized as Firebase projects, even those using googleAI():

import 'package:firebase_ai/firebase_ai.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_ai_toolkit/flutter_ai_toolkit.dart';

// from `flutterfire config`: https://firebase.google.com/docs/flutter/setup
import '../firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  runApp(const App());
}

// now you can use FirebaseAI.googleAI() or FirebaseAI.vertexAI()
Getting started 
Installation

Add the following dependencies to your pubspec.yaml file:

$ flutter pub add flutter_ai_toolkit firebase_ai firebase_core
Configuration

To use Firebase AI in your project, follow the steps described in Get started with the Gemini API using the Firebase AI Logic SDKs .

After following these instructions, you're ready to use Firebase AI in your Flutter app. Start by initializing Firebase:

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_vertexai/firebase_ai.dart';
import 'package:flutter_ai_toolkit/flutter_ai_toolkit.dart';

// Other app imports...

import 'firebase_options.dart'; // Generated by `flutterfire config`.

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  runApp(const App());
}

// Implementation of App and other widgets...
With Firebase properly initialized in your Flutter app, you're now ready to create an instance of the Firebase provider:

class ChatPage extends StatelessWidget {
  const ChatPage({super.key});

  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: AppBar(title: const Text(App.title)),
        body: LlmChatView(
          provider: FirebaseProvider(
            model: FirebaseAI.vertexAI().generativeModel(
              model: 'gemini-2.0-flash'
            ),
          ),
        ),
      );
}
The FirebaseAI class comes from the firebase_ai package. The AI Toolkit provides the FirebaseProvider class to expose Firebase AI to the LlmChatView. Note that you provide a model name that corresponds to one of the available model names.

For a complete usage example, check out the vertex.dart sample.

Set up device permissions

To enable your users to take advantage of features like voice input and media attachments, ensure that your app has the necessary permissions:

Network access:
To enable network access on macOS, add the following to your *.entitlements files:

<plist version="1.0">
    <dict>
      ...
      <key>com.apple.security.network.client</key>
      <true/>
    </dict>
</plist>
To enable network access on Android, ensure that your AndroidManifest.xml file contains the following:

<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    ...
    <uses-permission android:name="android.permission.INTERNET"/>
</manifest>
Microphone access: To enable voice input for users, update configs according to the permission and setup instructions for package:record.

File selection: To enable users to select and attach files, follow the usage instructions for package:file_selector.

Image selection:

To enable users to take or select a picture from their device, refer to the installation instructions for package:image_picker.

Samples 
To run the example apps in the example/lib directory, first replace the example/lib/firebase_options.dart file by running flutterfire config (you may need to manually delete this file first). The provided firebase_options.dart file is a placeholder for configuration needed by the examples.

## source code
llm_chat_view.dart
// Copyright 2024 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import 'dart:async';

import 'package:cross_file/cross_file.dart';
import 'package:flutter/widgets.dart';

import '../../chat_view_model/chat_view_model.dart';
import '../../chat_view_model/chat_view_model_provider.dart';
import '../../dialogs/adaptive_dialog.dart';
import '../../dialogs/adaptive_snack_bar/adaptive_snack_bar.dart';
import '../../llm_exception.dart';
import '../../platform_helper/platform_helper.dart' as ph;
import '../../providers/interface/attachments.dart';
import '../../providers/interface/chat_message.dart';
import '../../providers/interface/llm_provider.dart';
import '../../styles/llm_chat_view_style.dart';
import '../chat_history_view.dart';
import '../chat_input/chat_input.dart';
import '../response_builder.dart';
import 'llm_response.dart';

/// A widget that displays a chat interface for interacting with an LLM
/// (Language Model).
///
/// This widget provides a complete chat interface, including a message history
/// view and an input area for sending new messages. It is configured with an
/// [LlmProvider] to manage the chat interactions.
///
/// Example usage:
/// ```dart
/// LlmChatView(
///   provider: MyLlmProvider(),
///   style: LlmChatViewStyle(
///     backgroundColor: Colors.white,
///     // ... other style properties
///   ),
/// )
/// ```
@immutable
class LlmChatView extends StatefulWidget {
  /// Creates an [LlmChatView] widget.
  ///
  /// This widget provides a chat interface for interacting with an LLM
  /// (Language Model). It requires an [LlmProvider] to manage the chat
  /// interactions and can be customized with various style and configuration
  /// options.
  ///
  /// - [provider]: The [LlmProvider] that manages the chat interactions.
  /// - [style]: Optional. The [LlmChatViewStyle] to customize the appearance of
  ///   the chat interface.
  /// - [responseBuilder]: Optional. A custom [ResponseBuilder] to handle the
  ///   display of LLM responses.
  /// - [messageSender]: Optional. A custom [LlmStreamGenerator] to handle the
  ///   sending of messages. If provided, this is used instead of the
  ///   `sendMessageStream` method of the provider. It's the responsibility of
  ///   the caller to ensure that the [messageSender] properly streams the
  ///   response. This is useful for augmenting the user's prompt with
  ///   additional information, in the case of prompt engineering or RAG. It's
  ///   also useful for simple logging.
  /// - [suggestions]: Optional. A list of predefined suggestions to display
  ///   when the chat history is empty. Defaults to an empty list.
  /// - [welcomeMessage]: Optional. A welcome message to display when the chat
  ///   is first opened.
  /// - [onCancelCallback]: Optional. The action to perform when the user
  ///   cancels a chat operation. By default, a snackbar is displayed with the
  ///   canceled message.
  /// - [onErrorCallback]: Optional. The action to perform when an
  ///   error occurs during a chat operation. By default, an alert dialog is
  ///   displayed with the error message.
  /// - [cancelMessage]: Optional. The message to display when the user cancels
  ///   a chat operation. Defaults to 'CANCEL'.
  /// - [errorMessage]: Optional. The message to display when an error occurs
  ///   during a chat operation. Defaults to 'ERROR'.
  /// - [enableAttachments]: Optional. Whether to enable file and image attachments in the chat input.
  /// - [enableVoiceNotes]: Optional. Whether to enable voice notes in the chat input.
  LlmChatView({
    required LlmProvider provider,
    LlmChatViewStyle? style,
    ResponseBuilder? responseBuilder,
    LlmStreamGenerator? messageSender,
    List<String> suggestions = const [],
    String? welcomeMessage,
    this.onCancelCallback,
    this.onErrorCallback,
    this.cancelMessage = 'CANCEL',
    this.errorMessage = 'ERROR',
    this.enableAttachments = true,
    this.enableVoiceNotes = true,
    super.key,
  }) : viewModel = ChatViewModel(
         provider: provider,
         responseBuilder: responseBuilder,
         messageSender: messageSender,
         style: style,
         suggestions: suggestions,
         welcomeMessage: welcomeMessage,
         enableAttachments: enableAttachments,
         enableVoiceNotes: enableVoiceNotes,
       );

  /// Whether to enable file and image attachments in the chat input.
  ///
  /// When set to false, the attachment button and related functionality will be
  /// disabled.
  final bool enableAttachments;

  /// Whether to enable voice notes in the chat input.
  ///
  /// When set to false, the voice recording button and related functionality
  /// will be disabled.
  final bool enableVoiceNotes;

  /// The view model containing the chat state and configuration.
  ///
  /// This [ChatViewModel] instance holds the LLM provider, transcript,
  /// response builder, welcome message, and LLM icon for the chat interface.
  /// It encapsulates the core data and functionality needed for the chat view.
  late final ChatViewModel viewModel;

  /// The action to perform when the user cancels a chat operation.
  ///
  /// By default, a snackbar is displayed with the canceled message.
  final void Function(BuildContext context)? onCancelCallback;

  /// The action to perform when an error occurs during a chat operation.
  ///
  /// By default, an alert dialog is displayed with the error message.
  final void Function(BuildContext context, LlmException error)?
  onErrorCallback;

  /// The text message to display when the user cancels a chat operation.
  ///
  /// Defaults to 'CANCEL'.
  final String cancelMessage;

  /// The text message to display when an error occurs during a chat operation.
  ///
  /// Defaults to 'ERROR'.
  final String errorMessage;

  @override
  State<LlmChatView> createState() => _LlmChatViewState();
}

class _LlmChatViewState extends State<LlmChatView>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  LlmResponse? _pendingPromptResponse;
  ChatMessage? _initialMessage;
  ChatMessage? _associatedResponse;
  LlmResponse? _pendingSttResponse;

  @override
  void initState() {
    super.initState();
    widget.viewModel.provider.addListener(_onHistoryChanged);
  }

  @override
  void dispose() {
    super.dispose();
    widget.viewModel.provider.removeListener(_onHistoryChanged);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // for AutomaticKeepAliveClientMixin

    final chatStyle = LlmChatViewStyle.resolve(widget.viewModel.style);
    return ListenableBuilder(
      listenable: widget.viewModel.provider,
      builder:
          (context, child) => ChatViewModelProvider(
            viewModel: widget.viewModel,
            child: GestureDetector(
              onTap: () {
                // Dismiss keyboard when tapping anywhere in the view
                FocusScope.of(context).unfocus();
              },
              child: Container(
                color: chatStyle.backgroundColor,
                child: Column(
                  children: [
                    Expanded(
                      child: Stack(
                        children: [
                          ChatHistoryView(
                            // can only edit if we're not waiting on the LLM or if
                            // we're not already editing an LLM response
                            onEditMessage:
                                _pendingPromptResponse == null &&
                                        _associatedResponse == null
                                    ? _onEditMessage
                                    : null,
                            onSelectSuggestion: _onSelectSuggestion,
                          ),
                        ],
                      ),
                    ),
                    ChatInput(
                      initialMessage: _initialMessage,
                      autofocus: widget.viewModel.suggestions.isEmpty,
                      onCancelEdit:
                          _associatedResponse != null ? _onCancelEdit : null,
                      onSendMessage: _onSendMessage,
                      onCancelMessage:
                          _pendingPromptResponse == null
                              ? null
                              : _onCancelMessage,
                      onTranslateStt: _onTranslateStt,
                      onCancelStt:
                          _pendingSttResponse == null ? null : _onCancelStt,
                    ),
                  ],
                ),
              ),
            ),
          ),
    );
  }

  Future<void> _onSendMessage(
    String prompt,
    Iterable<Attachment> attachments,
  ) async {
    _initialMessage = null;
    _associatedResponse = null;

    // check the viewmodel for a user-provided message sender to use instead
    final sendMessageStream =
        widget.viewModel.messageSender ??
        widget.viewModel.provider.sendMessageStream;

    _pendingPromptResponse = LlmResponse(
      stream: sendMessageStream(prompt, attachments: attachments),
      // update during the streaming response input so that the end-user can see
      // the response as it streams in
      onUpdate: (_) => setState(() {}),
      onDone: _onPromptDone,
    );

    setState(() {});
  }

  void _onPromptDone(LlmException? error) {
    setState(() => _pendingPromptResponse = null);
    unawaited(_showLlmException(error));
  }

  void _onCancelMessage() => _pendingPromptResponse?.cancel();

  void _onEditMessage(ChatMessage message) {
    assert(_pendingPromptResponse == null);

    // remove the last llm message
    final history = widget.viewModel.provider.history.toList();
    assert(history.last.origin.isLlm);
    final llmMessage = history.removeLast();

    // remove the last user message
    assert(history.last.origin.isUser);
    final userMessage = history.removeLast();

    // set the history to the new history
    widget.viewModel.provider.history = history;

    // set the text  to the last userMessage to provide initial prompt and
    // attachments for the user to edit
    setState(() {
      _initialMessage = userMessage;
      _associatedResponse = llmMessage;
    });
  }

  Future<void> _onTranslateStt(
    XFile file,
    Iterable<Attachment> currentAttachments,
  ) async {
    assert(widget.enableVoiceNotes);
    _initialMessage = null;
    _associatedResponse = null;

    // use the LLM to translate the attached audio to text
    const prompt =
        'translate the attached audio to text; provide the result of that '
        'translation as just the text of the translation itself. be careful to '
        'separate the background audio from the foreground audio and only '
        'provide the result of translating the foreground audio.';
    final attachments = [await FileAttachment.fromFile(file)];

    var response = '';
    _pendingSttResponse = LlmResponse(
      stream: widget.viewModel.provider.generateStream(
        prompt,
        attachments: attachments,
      ),
      onUpdate: (text) => response += text,
      onDone:
          (error) async =>
              _onSttDone(error, response, file, currentAttachments),
    );

    setState(() {});
  }

  Future<void> _onSttDone(
    LlmException? error,
    String response,
    XFile file,
    Iterable<Attachment> attachments,
  ) async {
    assert(_pendingSttResponse != null);
    setState(() {
      // Preserve any existing attachments from the current input
      _initialMessage = ChatMessage.user(response, attachments);
      _pendingSttResponse = null;
    });

    // delete the file now that the LLM has translated it
    unawaited(ph.deleteFile(file));

    // show any error that occurred
    unawaited(_showLlmException(error));
  }

  void _onCancelStt() => _pendingSttResponse?.cancel();

  Future<void> _showLlmException(LlmException? error) async {
    if (error == null) return;

    // stop from the progress from indicating in case there was a failure
    // before any text response happened; the progress indicator uses a null
    // text message to keep progressing. plus we don't want to just show an
    // empty LLM message.
    final llmMessage = widget.viewModel.provider.history.last;
    if (llmMessage.text == null) {
      llmMessage.append(
        error is LlmCancelException
            ? widget.cancelMessage
            : widget.errorMessage,
      );
    }

    switch (error) {
      case LlmCancelException():
        if (widget.onCancelCallback != null) {
          widget.onCancelCallback!(context);
        } else {
          AdaptiveSnackBar.show(context, 'LLM operation canceled by user');
        }
        break;
      case LlmFailureException():
      case LlmException():
        if (widget.onErrorCallback != null) {
          widget.onErrorCallback!(context, error);
        } else {
          await AdaptiveAlertDialog.show(
            context: context,
            content: Text(error.toString()),
            showOK: true,
          );
        }
    }
  }

  void _onSelectSuggestion(String suggestion) =>
      setState(() => _initialMessage = ChatMessage.user(suggestion, []));

  void _onHistoryChanged() {
    // if the history is cleared, clear the initial message
    if (widget.viewModel.provider.history.isEmpty) {
      setState(() {
        _initialMessage = null;
        _associatedResponse = null;
      });
    }
  }

  void _onCancelEdit() {
    assert(_initialMessage != null);
    assert(_associatedResponse != null);

    // add the original message and response back to the history
    final history = widget.viewModel.provider.history.toList();
    history.addAll([_initialMessage!, _associatedResponse!]);
    widget.viewModel.provider.history = history;

    setState(() {
      _initialMessage = null;
      _associatedResponse = null;
    });
  }
}

llm_response.dart
// Copyright 2024 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import 'dart:async';

import '../../llm_exception.dart';

/// Represents a response from an LLM (Language Learning Model).
///
/// This class manages the streaming of LLM responses, error handling, and
/// cleanup.
class LlmResponse {
  /// Creates an LlmResponse.
  ///
  /// [stream] is the stream of text chunks from the LLM. [onDone] is an
  /// optional callback for when the response is complete or encounters an
  /// error.
  LlmResponse({
    required Stream<String> stream,
    required this.onUpdate,
    required this.onDone,
  }) {
    _subscription = stream.listen(
      onUpdate,
      onDone: () => onDone(null),
      cancelOnError: true,
      onError: (err) => _close(_exception(err)),
    );
  }

  /// Callback function to be called when a new chunk is received from the
  /// response stream.
  final void Function(String text) onUpdate;

  /// Callback function to be called when the response is complete or encounters
  /// an error.
  final void Function(LlmException? error) onDone;

  /// Cancels the response stream.
  void cancel() => _close(const LlmCancelException());

  StreamSubscription<String>? _subscription;

  LlmException _exception(dynamic err) => switch (err) {
    (LlmCancelException _) => const LlmCancelException(),
    (final LlmFailureException ex) => ex,
    _ => LlmFailureException(err.toString()),
  };

  void _close(LlmException error) {
    assert(_subscription != null);
    unawaited(_subscription!.cancel());
    _subscription = null;
    onDone.call(error);
  }
}
