Yumcha是一个flutter AI聊天应用，可以连接多个提供商，会支持跨平台，例如桌面端和移动端。主要可以AI聊天或AI角色聊天（类似Sillytavern），软件没有自己的服务器，主要靠用户填自己的key和配置来使用。数据存本地 sqlite。

提供商主要是不同提供商类型例如openai gemini等，用户可以新增多个提供商配置，例如自定义提供商，提供商类型选择openai ，然后自己写自定义提供商的地址、key，并且自己添加多个支持的模型名字，应该只有openai和ollama支持自定义服务器地址
助手主要是存AI聊天的参数，例如系统提示词，AI参数例如topp 温度，上下文数量、是否流式输出、自定义headers 自定义body等），用户聊天的时候主要选择助手后聊天，聊天中可以切换提供商来聊天。助手不需要选择提供商
聊天的时候会根据选择的助手来创建，聊天过程中可以切换提供商。

角色聊天主要是可以用角色书世界书来和AI聊天，我目前用了langchain_dart来实现AI聊天的逻辑，未来还会支持mcp、文生图、TTS 、图片文件做附件发送给AI等AI聊天的功能。

UI 要求给用户最新的 material 3 ui 的好的体验。

技术栈：flutter, material ui 3
langchain: ^0.7.7+2
langchain_openai: ^0.7.3
http: ^1.1.0
logger: ^2.4.0
drift: ^2.16.0
sqlite3_flutter_libs: ^0.5.0
path_provider: ^2.1.0
path: ^1.8.0
