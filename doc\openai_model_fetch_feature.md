# OpenAI 模型获取功能

## 功能概述

在AI提供商编辑界面中，当选择OpenAI提供商类型时，会显示一个"获取模型"按钮，允许用户从OpenAI API直接获取可用的模型列表。

## 功能特点

### 🎯 智能显示
- 只有在选择OpenAI提供商类型时才显示"获取模型"按钮
- 其他提供商类型（Anthropic、Google、Ollama等）不显示此按钮

### 🔄 实时获取
- 使用openai_dart库直接调用OpenAI API
- 支持自定义base URL（如代理服务器或本地部署）
- 自动处理API认证和错误

### 🎨 用户友好
- 加载状态指示器
- 清晰的错误提示
- 模型选择对话框
- 避免重复添加已存在的模型

### 🧠 智能过滤
- 自动过滤出聊天模型，排除嵌入、图像生成等其他类型模型
- 为常见模型提供友好的显示名称
- 自动识别模型功能（如图像分析支持）

## 使用方法

1. **创建或编辑OpenAI提供商**
   - 在提供商编辑界面选择"OpenAI"类型
   - 填写有效的API Key
   - 可选：填写自定义base URL

2. **获取模型列表**
   - 点击"获取模型"按钮
   - 等待API响应（通常几秒钟）
   - 在弹出的对话框中选择要添加的模型

3. **模型管理**
   - 已添加的模型会显示绿色勾号
   - 可以手动编辑或删除模型
   - 支持启用/禁用模型

## 技术实现

### 核心组件
- `ModelListManager`: 模型列表管理组件
- `_fetchOpenAIModels()`: OpenAI API调用方法
- `openai_dart`: OpenAI API客户端库

### 错误处理
- API密钥无效
- 网络连接问题
- 服务器错误
- 超时处理

### 模型过滤规则
支持的聊天模型前缀：
- `gpt-3.5`
- `gpt-4`
- `gpt-4o`
- `text-davinci`
- `text-curie`
- `text-babbage`
- `text-ada`

### URL格式化
自动处理各种base URL格式：
- `https://api.openai.com` → `https://api.openai.com/v1`
- `https://api.openai.com/` → `https://api.openai.com/v1`
- `https://custom.api.com` → `https://custom.api.com/v1`

## 示例场景

### 场景1：使用官方OpenAI API
```
提供商类型: OpenAI
API Key: sk-xxx...
Base URL: (留空，使用默认)
```

### 场景2：使用代理服务器
```
提供商类型: OpenAI
API Key: sk-xxx...
Base URL: https://my-proxy.com/v1
```

### 场景3：使用本地部署
```
提供商类型: OpenAI
API Key: local-key
Base URL: http://localhost:8000
```

## 注意事项

1. **API密钥安全**: 确保API密钥的安全性，不要在公共场所暴露
2. **网络要求**: 需要能够访问OpenAI API或指定的代理服务器
3. **费用考虑**: 调用API可能产生少量费用（通常很少）
4. **频率限制**: 遵守OpenAI的API调用频率限制

## 故障排除

### 常见错误及解决方案

**错误**: "获取模型列表失败: Invalid API key"
**解决**: 检查API密钥是否正确且有效

**错误**: "获取模型列表失败: Connection timeout"
**解决**: 检查网络连接和base URL设置

**错误**: "获取模型列表失败: 401 Unauthorized"
**解决**: 验证API密钥权限和账户状态

**错误**: "获取模型列表失败: 429 Too Many Requests"
**解决**: 等待一段时间后重试，或检查API使用配额
