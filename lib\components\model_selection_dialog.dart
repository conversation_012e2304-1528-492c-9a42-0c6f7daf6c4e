import 'package:flutter/material.dart';
import '../models/ai_model.dart';

class ModelSelectionDialog extends StatefulWidget {
  final List<AiModel> availableModels;
  final List<AiModel> currentModels;
  final Function(List<AiModel>) onConfirm;

  const ModelSelectionDialog({
    super.key,
    required this.availableModels,
    required this.currentModels,
    required this.onConfirm,
  });

  @override
  State<ModelSelectionDialog> createState() => _ModelSelectionDialogState();
}

class _ModelSelectionDialogState extends State<ModelSelectionDialog> {
  late Set<String> _selectedModelIds;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    // 初始化已选择的模型ID集合
    _selectedModelIds = widget.currentModels.map((model) => model.name).toSet();
  }

  List<AiModel> get _filteredModels {
    if (_searchQuery.isEmpty) {
      return widget.availableModels;
    }

    return widget.availableModels.where((model) {
      final query = _searchQuery.toLowerCase();
      return model.name.toLowerCase().contains(query) ||
          model.effectiveDisplayName.toLowerCase().contains(query);
    }).toList();
  }

  void _toggleModel(AiModel model) {
    setState(() {
      if (_selectedModelIds.contains(model.name)) {
        _selectedModelIds.remove(model.name);
      } else {
        _selectedModelIds.add(model.name);
      }
    });
  }

  void _onConfirm() {
    // 构建最终的模型列表
    final selectedModels = <AiModel>[];

    // 添加从API获取的已选择模型
    for (final model in widget.availableModels) {
      if (_selectedModelIds.contains(model.name)) {
        selectedModels.add(model);
      }
    }

    // 添加当前模型中不在API列表中的模型（用户手动添加的）
    for (final currentModel in widget.currentModels) {
      final isInAvailable = widget.availableModels.any(
        (model) => model.name == currentModel.name,
      );

      if (!isInAvailable && _selectedModelIds.contains(currentModel.name)) {
        selectedModels.add(currentModel);
      }
    }

    widget.onConfirm(selectedModels);
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Row(
              children: [
                const Icon(Icons.model_training),
                const SizedBox(width: 8),
                Text('选择模型', style: Theme.of(context).textTheme.headlineSmall),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 搜索框
            TextField(
              decoration: const InputDecoration(
                labelText: '搜索模型',
                hintText: '输入模型名称进行搜索',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
            const SizedBox(height: 16),

            // 统计信息
            Text(
              '找到 ${_filteredModels.length} 个模型，已选择 ${_selectedModelIds.length} 个',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 16),

            // 模型列表
            Expanded(
              child: _filteredModels.isEmpty
                  ? const Center(
                      child: Text(
                        '没有找到匹配的模型',
                        style: TextStyle(color: Colors.grey),
                      ),
                    )
                  : ListView.builder(
                      itemCount: _filteredModels.length,
                      itemBuilder: (context, index) {
                        final model = _filteredModels[index];
                        final isSelected = _selectedModelIds.contains(
                          model.name,
                        );
                        final isFromCurrent = widget.currentModels.any(
                          (m) => m.name == model.name,
                        );

                        return Card(
                          margin: const EdgeInsets.only(bottom: 8),
                          child: CheckboxListTile(
                            value: isSelected,
                            onChanged: (_) => _toggleModel(model),
                            title: Row(
                              children: [
                                Expanded(child: Text(model.name)),
                                if (isFromCurrent)
                                  Container(
                                    margin: const EdgeInsets.only(left: 8),
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 2,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Theme.of(
                                        context,
                                      ).colorScheme.primaryContainer,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Text(
                                      '已添加',
                                      style: TextStyle(
                                        fontSize: 10,
                                        color: Theme.of(
                                          context,
                                        ).colorScheme.onPrimaryContainer,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                            controlAffinity: ListTileControlAffinity.trailing,
                          ),
                        );
                      },
                    ),
            ),

            const SizedBox(height: 16),

            // 操作按钮
            Row(
              children: [
                IconButton.outlined(
                  onPressed: () {
                    setState(() {
                      _selectedModelIds.clear();
                    });
                  },
                  icon: const Icon(Icons.clear_all),
                  tooltip: '全部取消',
                ),
                const SizedBox(width: 8),
                IconButton.outlined(
                  onPressed: () {
                    setState(() {
                      _selectedModelIds.addAll(
                        _filteredModels.map((model) => model.name),
                      );
                    });
                  },
                  icon: const Icon(Icons.select_all),
                  tooltip: '全部选择',
                ),
                const Spacer(),
                IconButton.outlined(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                  tooltip: '取消',
                ),
                const SizedBox(width: 8),
                FilledButton.icon(
                  onPressed: _onConfirm,
                  icon: const Icon(Icons.check),
                  label: const Text('确定'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
